import pywencai
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta, date
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.formatting.rule import ColorScaleRule
import os

# 全局缓存变量
_trade_dates_cache = None
_cache_time = None

def get_trade_dates():
    """获取交易日历数据"""
    global _trade_dates_cache, _cache_time

    # 检查缓存是否有效（1小时）
    if _trade_dates_cache is not None and _cache_time is not None:
        if (datetime.now() - _cache_time).seconds < 3600:
            return _trade_dates_cache

    try:
        print("正在获取交易日历数据...")
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])

        # 更新缓存
        _trade_dates_cache = trade_dates_df
        _cache_time = datetime.now()

        return trade_dates_df
    except Exception as e:
        print(f"获取交易日历数据失败: {str(e)}")
        return pd.DataFrame()

def is_trade_date(check_date):
    """检查指定日期是否为交易日"""
    try:
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            return True  # 如果无法获取数据，默认认为是交易日

        # 将检查日期转换为datetime
        if isinstance(check_date, date):
            check_datetime = datetime.combine(check_date, datetime.min.time())
        else:
            check_datetime = check_date

        # 检查是否在交易日列表中
        return not trade_dates_df[trade_dates_df['trade_date'].dt.date == check_datetime.date()].empty

    except Exception as e:
        print(f"检查交易日失败: {str(e)}")
        return True

def get_limit_up_stocks_data(query_date):
    """获取指定日期的涨停股票数据"""
    try:
        date_str = query_date.strftime('%Y%m%d')
        date_str_dot = query_date.strftime('%Y.%m.%d')
        print(f"正在查询 {date_str} 沪深主板涨停股票数据...")

        # 构建简化的查询语句，让问财自动返回相关字段
        query = f"{date_str}沪深主板,涨停股票,非ST,换手率,所属行业"

        print(f"查询语句: {query}")

        # 使用问财API查询
        df = pywencai.get(query=query, loop=True)

        if df is None or df.empty:
            print(f"未获取到 {date_str} 的涨停股票数据")
            return None

        print(f"成功获取到 {len(df)} 只涨停股票数据")

        # 显示原始数据列名用于调试
        print("原始数据列名预览:")
        print("所有列名:", list(df.columns))

        return df

    except Exception as e:
        print(f"获取涨停股票数据失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def process_limit_up_data(df, query_date):
    """处理涨停股票数据，只保留指定字段并进行数据处理"""
    try:
        print(f"成功获取 {len(df)} 条涨停股票数据")
        print(f"原始数据包含 {len(df.columns)} 个字段")

        date_str_dot = query_date.strftime('%Y.%m.%d')

        # 定义需要保留的字段关键词映射（关键词 -> 新字段名）
        # 按照在Excel中的显示顺序排列
        field_keywords = {
            '股票简称': '股票简称',
            '连续涨停天数': '连板',
            '涨停原因类别': '涨停原因类别',
            '几天几板': '几天几板',
            '涨停类型': '涨停类型',
            '首次涨停时间': '首次涨停',
            '最终涨停时间': '最终涨停',
            '涨停开板次数': '开板次',
            '换手率': '换手率',  # 去掉(%)
            'a股流通市值': '流通市值',  # 去掉(亿元)，调整到换手率后面
            'a股市值(不含限售股)': '流通市值',  # 去掉(亿元)
            '市盈率': '市盈率',  # 调整到流通市值后面
            '涨停封单量占成交量比': '封成比',  # 去掉(%)
            '涨停封单量占流通a股比': '封流比',  # 去掉(%)
            '涨停封单额': '封单额',  # 去掉(亿元)，调整到封流比后面
            '同花顺行业': '所属同花顺行业',
            '所属同花顺行业': '所属同花顺行业',
            '所属概念': '所属概念'
        }

        # 查找实际存在的字段
        result_data = []
        available_fields = {}
        missing_fields = []

        for keyword, new_field in field_keywords.items():
            found = False
            # 在所有列中查找包含关键词的字段
            for col in df.columns:
                col_str = str(col)
                if keyword in col_str:
                    available_fields[col] = new_field
                    found = True
                    break

            if not found:
                missing_fields.append(new_field)

        if missing_fields:
            print(f"⚠️ 以下字段未找到: {', '.join(missing_fields)}")

        print(f"✅ 成功匹配 {len(available_fields)} 个字段")

        # 处理数据
        for _, row in df.iterrows():
            row_data = {}
            for original_field, new_field in available_fields.items():
                value = row.get(original_field, '')

                # 对不同类型的字段进行特殊处理
                if '封单额' in new_field or '流通市值' in new_field:
                    # 金额字段：转换为亿元
                    try:
                        if pd.notna(value) and value != '':
                            # 处理可能包含单位的数值
                            value_str = str(value).replace(',', '').replace('亿', '').replace('万', '').replace('元', '')
                            numeric_value = float(value_str)

                            # 如果原始数据已经是亿为单位，直接使用；否则从元转换
                            if '亿' in str(value):
                                value = round(numeric_value, 2)
                            elif '万' in str(value):
                                value = round(numeric_value / 10000, 2)  # 万转换为亿
                            else:
                                value = round(numeric_value / 100000000, 2)  # 元转换为亿元，保留2位小数
                    except (ValueError, TypeError):
                        value = ''

                elif '封成比' in new_field or '封流比' in new_field:
                    # 百分比字段：保留小数点后的百分比
                    try:
                        if pd.notna(value) and value != '':
                            # 如果原始数据已经是百分比格式，直接使用
                            if isinstance(value, str) and '%' in value:
                                numeric_value = float(value.replace('%', ''))
                            else:
                                numeric_value = float(str(value))
                                # 如果数值大于1，可能需要转换为百分比
                                if numeric_value > 1:
                                    numeric_value = numeric_value  # 保持原值
                            value = round(numeric_value, 2)  # 保留2位小数
                    except (ValueError, TypeError):
                        value = ''

                elif '首次涨停' in new_field or '最终涨停' in new_field:
                    # 时间字段：格式化为 HH:MM
                    try:
                        if pd.notna(value) and value != '':
                            time_str = str(value)
                            # 如果包含日期，提取时间部分
                            if ' ' in time_str:
                                time_part = time_str.split(' ')[-1]
                            else:
                                time_part = time_str

                            # 确保格式为 HH:MM
                            if ':' in time_part:
                                time_parts = time_part.split(':')
                                if len(time_parts) >= 2:
                                    hour = time_parts[0].zfill(2)
                                    minute = time_parts[1].zfill(2)
                                    value = f"{hour}:{minute}"
                    except (ValueError, TypeError):
                        value = ''

                elif '市盈率' in new_field:
                    # 市盈率字段：保留2位小数
                    try:
                        if pd.notna(value) and value != '':
                            # 处理可能的负值或特殊值
                            value_str = str(value).replace(',', '')
                            if value_str.lower() in ['--', '-', 'nan', 'null', '']:
                                value = ''
                            else:
                                numeric_value = float(value_str)
                                value = round(numeric_value, 2)
                    except (ValueError, TypeError):
                        value = ''

                elif '开板次' in new_field:
                    # 开板次数字段：整数格式
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).replace(',', '').replace('次', '')
                            if value_str.lower() in ['--', '-', 'nan', 'null', '']:
                                value = 0
                            else:
                                value = int(float(value_str))
                    except (ValueError, TypeError):
                        value = 0

                elif '换手率' in new_field:
                    # 换手率字段：百分比格式，保留1位小数
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).replace(',', '').replace('%', '')
                            if value_str.lower() in ['--', '-', 'nan', 'null', '']:
                                value = ''
                            else:
                                numeric_value = float(value_str)
                                # 如果原始数据已经是百分比格式，直接使用；否则可能需要转换
                                if '%' in str(value):
                                    value = round(numeric_value, 1)
                                else:
                                    # 如果数值小于1，可能是小数形式，需要转换为百分比
                                    if numeric_value <= 1:
                                        value = round(numeric_value * 100, 1)
                                    else:
                                        value = round(numeric_value, 1)
                    except (ValueError, TypeError):
                        value = ''

                elif '几天几板' in new_field:
                    # 几天几板字段：将"首板涨停"处理为"首板"
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).strip()
                            # 将"首板涨停"替换为"首板"
                            if value_str == '首板涨停':
                                value = '首板'
                            else:
                                value = value_str
                        else:
                            value = ''
                    except (ValueError, TypeError):
                        value = ''

                elif '涨停类型' in new_field:
                    # 涨停类型字段：去掉"涨停"，将"||"替换为"|"
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).strip()
                            # 去掉"涨停"
                            value_str = value_str.replace('涨停', '')
                            # 将"||"替换为"|"
                            value_str = value_str.replace('||', '|')
                            # 清理可能的多余空格和分隔符
                            value_str = value_str.strip('|').strip()
                            value = value_str
                        else:
                            value = ''
                    except (ValueError, TypeError):
                        value = ''

                row_data[new_field] = value
            result_data.append(row_data)

        result_df = pd.DataFrame(result_data)

        print(f"成功处理 {len(result_df)} 条数据，包含 {len(result_df.columns)} 个字段")

        # 显示处理后的数据预览（只显示第一行）
        print("\n数据预览（第一行）:")
        if not result_df.empty:
            first_row = result_df.iloc[0:1]
            # 转置显示，便于查看
            first_row_transposed = first_row.T
            first_row_transposed.columns = ['值']
            print(first_row_transposed.to_string())

        return result_df

    except Exception as e:
        print(f"处理涨停股票数据失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None



def create_new_sheet_with_data(file_path, new_sheet_name, data_df):
    """在Excel文件中创建新的sheet并写入数据，包含格式化和排序"""
    try:
        # 数据排序：首先按连板降序，再按首次涨停时间升序
        print("正在对数据进行排序...")
        if '连板' in data_df.columns and '首次涨停' in data_df.columns:
            # 处理首次涨停时间排序（转换为可排序的格式）
            def time_to_minutes(time_str):
                try:
                    if pd.notna(time_str) and time_str != '':
                        parts = str(time_str).split(':')
                        if len(parts) == 2:
                            return int(parts[0]) * 60 + int(parts[1])
                    return 9999  # 无效时间排到最后
                except:
                    return 9999

            data_df['_sort_time'] = data_df['首次涨停'].apply(time_to_minutes)
            data_df = data_df.sort_values(['连板', '_sort_time'], ascending=[False, True])
            data_df = data_df.drop('_sort_time', axis=1)  # 删除临时排序列

        # 加载现有的Excel文件
        workbook = openpyxl.load_workbook(file_path)

        # 如果sheet已存在，先删除
        if new_sheet_name in workbook.sheetnames:
            workbook.remove(workbook[new_sheet_name])
            print(f"已删除现有的 '{new_sheet_name}' sheet")

        # 创建新的sheet
        new_sheet = workbook.create_sheet(title=new_sheet_name)

        # 定义样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")
        left_alignment = Alignment(horizontal="left", vertical="center")
        thin_border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )

        # 写入表头并设置样式
        headers = list(data_df.columns)
        for col_idx, header in enumerate(headers, 1):
            cell = new_sheet.cell(row=1, column=col_idx, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = thin_border

        # 写入数据并设置样式
        for row_idx, (_, row) in enumerate(data_df.iterrows(), 2):
            for col_idx, (col_name, value) in enumerate(zip(headers, row), 1):
                cell = new_sheet.cell(row=row_idx, column=col_idx, value=value)
                cell.border = thin_border

                # 根据字段类型设置对齐方式
                if col_name in ['所属同花顺行业', '所属概念']:
                    cell.alignment = left_alignment  # 文本字段左对齐
                else:
                    cell.alignment = center_alignment  # 其他字段居中

        # 自动调整列宽（确保所有列都适应内容）
        print("正在调整列宽...")

        # 遍历所有列进行宽度调整
        for col_num in range(1, len(headers) + 1):
            column_letter = chr(64 + col_num)  # A, B, C, ...
            max_length = 0
            header_name = headers[col_num - 1]

            # 检查该列的所有单元格内容（包括表头）
            for row_num in range(1, len(data_df) + 2):  # +2 因为包含表头
                try:
                    cell = new_sheet.cell(row=row_num, column=col_num)
                    cell_value = str(cell.value) if cell.value is not None else ""

                    # 考虑中文字符占用更多空间
                    chinese_chars = len([c for c in cell_value if ord(c) > 127])
                    english_chars = len(cell_value) - chinese_chars
                    # 中文字符按1.8倍计算宽度（更准确）
                    cell_length = english_chars + chinese_chars * 1.8

                    if cell_length > max_length:
                        max_length = cell_length
                except Exception as e:
                    continue

            # 根据列内容类型设置不同的宽度策略
            if header_name in ['所属同花顺行业', '所属概念']:
                # 文本类字段，允许更宽，但有上限
                adjusted_width = min(max(max_length + 3, 15), 45)
            elif header_name in ['股票简称']:
                # 股票名称，适中宽度
                adjusted_width = max(min(max_length + 2, 14), 8)
            elif header_name in ['首次涨停', '最终涨停']:
                # 时间字段，固定宽度
                adjusted_width = max(max_length + 1, 8)
            elif header_name in ['涨停原因类别']:
                # 原因类别，可能较长
                adjusted_width = min(max(max_length + 2, 12), 25)
            elif header_name in ['几天几板']:
                # 几天几板，固定宽度
                adjusted_width = max(max_length + 1, 8)
            elif header_name in ['连板', '开板次']:
                # 数字字段，较窄
                adjusted_width = max(max_length + 1, 6)
            elif header_name in ['换手率', '封成比', '封流比']:
                # 百分比字段
                adjusted_width = max(max_length + 1, 8)
            elif header_name in ['封单额', '流通市值']:
                # 金额字段
                adjusted_width = max(max_length + 1, 10)
            elif header_name in ['市盈率']:
                # 市盈率字段
                adjusted_width = max(max_length + 1, 7)
            else:
                # 其他字段，通用设置
                adjusted_width = max(min(max_length + 2, 20), 8)

            # 应用列宽
            new_sheet.column_dimensions[column_letter].width = adjusted_width

        print(f"✅ 已调整 {len(headers)} 列的宽度")

        # 添加多种条件格式
        print("正在应用条件格式...")

        # 1. 连板条件格式（天数越大颜色越深）
        if '连板' in headers:
            limit_days_col = headers.index('连板') + 1

            # 获取连板天数的唯一值并排序（从小到大）
            unique_days = sorted(data_df['连板'].dropna().unique())

            # 为不同天数设置不同颜色（天数越大颜色越深）
            colors = [
                "FFE6E6",  # 1天：浅红
                "FFCCCC",  # 2天：较浅红
                "FFB3B3",  # 3天：中等红
                "FF9999",  # 4天：较深红
                "FF8080",  # 5天：深红
                "FF6666",  # 6天：更深红
                "FF4D4D",  # 7天：很深红
                "FF3333",  # 8天及以上：极深红
            ]

            from openpyxl.formatting.rule import CellIsRule
            for i, days in enumerate(unique_days):
                if i < len(colors):
                    color = colors[i]
                else:
                    color = colors[-1]  # 超过8天的都用最深的红色

                rule = CellIsRule(
                    operator='equal',
                    formula=[str(days)],
                    fill=PatternFill(start_color=color, end_color=color, fill_type="solid")
                )
                new_sheet.conditional_formatting.add(
                    f"{chr(64 + limit_days_col)}{2}:{chr(64 + limit_days_col)}{len(data_df) + 1}",
                    rule
                )

        # 2. 封成比和封流比条件格式（根据流通市值设置不同阈值）
        if '流通市值' in headers:
            market_value_col = headers.index('流通市值') + 1

            # 封成比条件格式
            if '封成比' in headers:
                seal_ratio_col = headers.index('封成比') + 1

                for row_idx in range(2, len(data_df) + 2):
                    try:
                        market_value = data_df.iloc[row_idx - 2]['流通市值']
                        seal_ratio = data_df.iloc[row_idx - 2]['封成比']

                        if pd.notna(market_value) and pd.notna(seal_ratio):
                            market_value = float(market_value)
                            seal_ratio = float(seal_ratio)

                            # 根据流通市值确定封成比阈值
                            if market_value < 30:
                                threshold = 15.0  # > 15倍
                            elif market_value <= 100:
                                threshold = 10.0  # > 10倍
                            else:
                                threshold = 5.0   # > 5倍

                            # 如果封成比超过阈值，标注为蓝色
                            if seal_ratio > threshold:
                                cell = new_sheet.cell(row=row_idx, column=seal_ratio_col)
                                cell.fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
                    except:
                        continue

            # 封流比条件格式
            if '封流比' in headers:
                flow_ratio_col = headers.index('封流比') + 1

                for row_idx in range(2, len(data_df) + 2):
                    try:
                        market_value = data_df.iloc[row_idx - 2]['流通市值']
                        flow_ratio = data_df.iloc[row_idx - 2]['封流比']

                        if pd.notna(market_value) and pd.notna(flow_ratio):
                            market_value = float(market_value)
                            flow_ratio = float(flow_ratio)

                            # 根据流通市值确定封流比阈值
                            if market_value < 30:
                                threshold = 1.2   # > 1.2%
                            elif market_value <= 100:
                                threshold = 0.7   # > 0.7%
                            else:
                                threshold = 0.4   # > 0.4%

                            # 如果封流比超过阈值，标注为蓝色
                            if flow_ratio > threshold:
                                cell = new_sheet.cell(row=row_idx, column=flow_ratio_col)
                                cell.fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
                    except:
                        continue

        # 3. 流通市值条件格式（200-500亿淡蓝色，500-1000亿加深，1000亿以上深蓝色）
        if '流通市值' in headers:
            market_value_col = headers.index('流通市值') + 1

            for row_idx in range(2, len(data_df) + 2):
                try:
                    market_value = data_df.iloc[row_idx - 2]['流通市值']
                    if pd.notna(market_value) and market_value != '':
                        market_value = float(market_value)
                        cell = new_sheet.cell(row=row_idx, column=market_value_col)

                        # 根据流通市值设置不同深度的蓝色
                        if 200 <= market_value < 500:
                            # 200-500亿：淡蓝色
                            cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                        elif 500 <= market_value < 1000:
                            # 500-1000亿：中等蓝色
                            cell.fill = PatternFill(start_color="B3D9FF", end_color="B3D9FF", fill_type="solid")
                        elif market_value >= 1000:
                            # 1000亿以上：深蓝色
                            cell.fill = PatternFill(start_color="4D94FF", end_color="4D94FF", fill_type="solid")
                            # 深蓝色背景用白色字体
                            cell.font = Font(color="FFFFFF")
                except:
                    continue

        # 4. 最终涨停时间条件格式（9:25及之前红色加深标注，9:30-10:00时间越向后颜色越淡）
        if '最终涨停' in headers:
            final_limit_col = headers.index('最终涨停') + 1

            for row_idx in range(2, len(data_df) + 2):
                try:
                    final_time = data_df.iloc[row_idx - 2]['最终涨停']
                    if pd.notna(final_time) and final_time != '':
                        time_str = str(final_time)
                        if ':' in time_str:
                            hour, minute = map(int, time_str.split(':'))
                            time_minutes = hour * 60 + minute

                            cell = new_sheet.cell(row=row_idx, column=final_limit_col)

                            # 9:25及之前：红色加深标注
                            if time_minutes <= 565:  # 9:25及之前
                                cell.fill = PatternFill(start_color="CC0000", end_color="CC0000", fill_type="solid")
                                # 深红色背景用白色字体
                                cell.font = Font(color="FFFFFF", bold=True)
                            # 9:30:00-10:00:00 按时间渐变（时间越向后颜色越淡）
                            elif 570 <= time_minutes <= 600:  # 9:30-10:00
                                # 计算渐变色（从深红到浅红）
                                progress = (time_minutes - 570) / 30  # 0-1之间

                                # 使用渐变颜色
                                colors = [
                                    "FF3333",  # 9:30 深红
                                    "FF4D4D",  # 较深红
                                    "FF6666",  # 更深红
                                    "FF8080",  # 深红
                                    "FF9999",  # 较深红
                                    "FFB3B3",  # 中等红
                                    "FFCCCC",  # 较浅红
                                    "FFE6E6",  # 10:00 浅红
                                ]

                                # 根据时间进度选择颜色
                                color_index = int(progress * (len(colors) - 1))
                                color = colors[color_index]

                                cell.fill = PatternFill(start_color=color, end_color=color, fill_type="solid")
                                # 保持黑色字体
                                cell.font = Font(color="000000")
                except:
                    continue

        # 5. 换手率高风险标注（根据流通市值设置不同阈值）
        if '换手率' in headers and '流通市值' in headers:
            turnover_col = headers.index('换手率') + 1

            for row_idx in range(2, len(data_df) + 2):
                try:
                    market_value = data_df.iloc[row_idx - 2]['流通市值']
                    turnover = data_df.iloc[row_idx - 2]['换手率']

                    if pd.notna(market_value) and pd.notna(turnover):
                        market_value = float(market_value)
                        turnover = float(turnover)

                        # 根据流通市值确定高风险阈值
                        if market_value < 20:
                            threshold = 25.0
                        elif market_value <= 50:
                            threshold = 20.0
                        elif market_value <= 100:
                            threshold = 15.0
                        else:
                            threshold = 12.0

                        # 如果换手率超过阈值，标注为黄色
                        if turnover > threshold:
                            cell = new_sheet.cell(row=row_idx, column=turnover_col)
                            cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
                except:
                    continue

        # 保存文件
        workbook.save(file_path)

        print(f"✅ 成功创建新sheet '{new_sheet_name}' 并写入 {len(data_df)} 条数据")
        print("✅ 已应用排序、格式化和条件格式")
        return True

    except Exception as e:
        print(f"创建新sheet失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def get_latest_trade_date():
    """获取最近的交易日"""
    try:
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            # 如果无法获取交易日历，使用简单逻辑
            current_date = datetime.now().date()
            # 如果是周末，往前推到周五
            if current_date.weekday() == 5:  # 周六
                return current_date - timedelta(days=1)
            elif current_date.weekday() == 6:  # 周日
                return current_date - timedelta(days=2)
            else:
                return current_date

        # 获取最近的交易日
        current_datetime = datetime.now()
        past_dates = trade_dates_df[trade_dates_df['trade_date'] <= current_datetime]

        if not past_dates.empty:
            latest_trade_date = past_dates.iloc[-1]['trade_date']
            return latest_trade_date.date()
        else:
            return datetime.now().date()

    except Exception as e:
        print(f"获取最近交易日失败: {str(e)}")
        # 使用简单逻辑作为备选
        current_date = datetime.now().date()
        if current_date.weekday() == 5:  # 周六
            return current_date - timedelta(days=1)
        elif current_date.weekday() == 6:  # 周日
            return current_date - timedelta(days=2)
        else:
            return current_date

def get_date_range_from_trade_dates(start_date, end_date):
    """从交易日历中获取指定范围内的所有交易日"""
    try:
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            # 如果无法获取交易日历，使用简单逻辑生成日期范围
            dates = []
            current = start_date
            while current <= end_date:
                # 排除周末
                if current.weekday() < 5:  # 0-4是周一到周五
                    dates.append(current)
                current += timedelta(days=1)
            return dates

        # 从交易日历中筛选指定范围的交易日
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.min.time())

        filtered_dates = trade_dates_df[
            (trade_dates_df['trade_date'] >= start_datetime) &
            (trade_dates_df['trade_date'] <= end_datetime)
        ]

        return [d.date() for d in filtered_dates['trade_date']]

    except Exception as e:
        print(f"获取交易日范围失败: {str(e)}")
        return []

def get_user_date_choice():
    """获取用户的日期查询选择"""
    print("📊 涨停股票数据查询工具")
    print("=" * 50)
    print("📅 查询模式选择")
    print("1. 单日查询")
    print("2. 日期范围查询")

    while True:
        choice = input("请选择查询模式 (1/2): ").strip()
        if choice in ['1', '2']:
            return choice
        print("❌ 请输入 1 或 2")

def get_single_date():
    """获取单个查询日期"""
    # 默认选择最近的交易日
    default_date = get_latest_trade_date()
    default_date_str = default_date.strftime('%Y%m%d')

    print(f"\n📅 单日查询设置")
    print(f"默认日期: {default_date_str}")

    while True:
        date_input = input(f"请输入查询日期 (格式: YYYYMMDD, 直接回车使用默认日期 {default_date_str}): ").strip()

        if not date_input:
            query_date = default_date
        else:
            try:
                query_date = datetime.strptime(date_input, '%Y%m%d').date()
            except ValueError:
                print("❌ 日期格式错误，请使用 YYYYMMDD 格式")
                continue

        # 检查是否为交易日
        if is_trade_date(query_date):
            print(f"✅ {query_date.strftime('%Y%m%d')} 是交易日")
            break
        else:
            print(f"⚠️ {query_date.strftime('%Y%m%d')} 不是交易日")
            confirm = input("是否继续使用此日期? (y/n): ").strip().lower()
            if confirm in ['y', 'yes']:
                break

    return [query_date]

def get_date_range():
    """获取日期范围"""
    print(f"\n� 日期范围查询设置")

    while True:
        try:
            start_input = input("请输入开始日期 (格式: YYYYMMDD): ").strip()
            start_date = datetime.strptime(start_input, '%Y%m%d').date()
            break
        except ValueError:
            print("❌ 开始日期格式错误，请使用 YYYYMMDD 格式")

    while True:
        try:
            end_input = input("请输入结束日期 (格式: YYYYMMDD): ").strip()
            end_date = datetime.strptime(end_input, '%Y%m%d').date()

            if end_date < start_date:
                print("❌ 结束日期不能早于开始日期")
                continue
            break
        except ValueError:
            print("❌ 结束日期格式错误，请使用 YYYYMMDD 格式")

    # 获取范围内的交易日
    print(f"正在获取 {start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')} 范围内的交易日...")
    trade_dates = get_date_range_from_trade_dates(start_date, end_date)

    if not trade_dates:
        print("❌ 未找到指定范围内的交易日")
        return []

    print(f"✅ 找到 {len(trade_dates)} 个交易日:")
    for i, date in enumerate(trade_dates, 1):
        print(f"  {i}. {date.strftime('%Y%m%d')} ({date.strftime('%Y-%m-%d %A')})")

    confirm = input(f"\n是否查询这 {len(trade_dates)} 个交易日的数据? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return []

    return trade_dates

def get_user_dates():
    """获取用户输入的查询日期（单日或范围）"""
    choice = get_user_date_choice()

    if choice == '1':
        dates = get_single_date()
    else:
        dates = get_date_range()

    if dates:
        print(f"\n📈 将查询 {len(dates)} 个交易日的数据")
        print("🎯 查询内容: 沪深主板涨停股票(非ST)")
        print("将获取问财的全部信息并储存到sheet中")

    return dates

def get_default_file_path():
    """获取默认的Excel文件路径"""
    default_folder = r"C:\Users\<USER>\Nutstore\1\我的坚果云"
    default_filename = "涨停情况.xlsx"
    default_file_path = os.path.join(default_folder, default_filename)

    print(f"📁 默认Excel文件: {default_file_path}")

    # 检查默认文件是否存在
    if os.path.exists(default_file_path):
        print("✅ 默认文件存在")
        return default_file_path
    else:
        # 如果文件不存在，检查文件夹是否存在
        if os.path.exists(default_folder):
            print("📝 默认文件不存在，将创建新文件")
            return default_file_path
        else:
            print(f"❌ 默认路径不存在: {default_folder}")
            return None

def sort_worksheets_by_date(file_path, query_dates):
    """对工作表按日期排序，最新日期在最左侧（最前面）"""
    try:
        # 加载Excel文件
        workbook = openpyxl.load_workbook(file_path)

        # 创建日期到工作表名称的映射
        date_to_sheet = {}
        for date in query_dates:
            sheet_name = date.strftime('%m%d')
            if sheet_name in workbook.sheetnames:
                date_to_sheet[date] = sheet_name

        # 按日期降序排序（最新日期在前）
        sorted_dates = sorted(date_to_sheet.keys(), reverse=True)

        # 获取所有工作表名称
        all_sheets = workbook.sheetnames.copy()

        # 找出不是本次创建的工作表（保持原有位置）
        other_sheets = [sheet for sheet in all_sheets if sheet not in date_to_sheet.values()]

        # 重新排列工作表顺序：按日期排序的新工作表（最新在前） + 其他工作表
        new_order = [date_to_sheet[date] for date in sorted_dates] + other_sheets

        # 重新创建工作表顺序
        # 先移除所有工作表引用，然后按新顺序重新添加
        temp_sheets = []
        for sheet_name in new_order:
            if sheet_name in workbook.sheetnames:
                temp_sheets.append((sheet_name, workbook[sheet_name]))

        # 删除所有工作表
        for sheet_name in workbook.sheetnames.copy():
            del workbook[sheet_name]

        # 按新顺序重新添加工作表
        for sheet_name, sheet in temp_sheets:
            workbook._add_sheet(sheet)

        # 保存文件
        workbook.save(file_path)
        workbook.close()

        return True

    except Exception as e:
        print(f"工作表排序失败: {str(e)}")
        return False

def process_single_date(query_date, file_path):
    """处理单个日期的涨停数据"""
    # 新sheet名称设置（使用日期的最后4位）
    new_sheet_name = query_date.strftime('%m%d')

    print(f"\n� 处理日期: {query_date.strftime('%Y%m%d')}")
    print(f"📋 工作表名称: {new_sheet_name}")

    # 查询涨停股票数据
    limit_up_df = get_limit_up_stocks_data(query_date)

    if limit_up_df is not None:
        # 处理数据
        print("正在处理数据...")
        processed_df = process_limit_up_data(limit_up_df, query_date)

        if processed_df is not None:
            # 显示统计信息
            print(f"📊 {query_date.strftime('%Y%m%d')} 数据处理完成！")
            print(f"涨停股票总数: {len(processed_df)}")

            # 尝试显示一些基本统计（如果字段存在）
            for col in processed_df.columns:
                col_str = str(col)
                if '连板' in col_str:
                    try:
                        avg_days = processed_df[col].mean()
                        print(f"平均连板天数: {avg_days:.1f}")
                        break
                    except:
                        pass

            # 创建新sheet
            print(f"正在创建工作表 '{new_sheet_name}'...")
            success = create_new_sheet_with_data(file_path, new_sheet_name, processed_df)

            if success:
                print(f"✅ {query_date.strftime('%Y%m%d')} 数据写入完成！")
                return True
            else:
                print(f"❌ {query_date.strftime('%Y%m%d')} 创建工作表失败")
                return False
        else:
            print(f"❌ {query_date.strftime('%Y%m%d')} 数据处理失败")
            return False
    else:
        print(f"❌ {query_date.strftime('%Y%m%d')} 查询数据失败")
        return False

def main():
    try:
        # 获取查询日期（单日或范围）
        query_dates = get_user_dates()

        if not query_dates:
            print("❌ 未获取到有效的查询日期，程序退出")
            return

        # 获取默认Excel文件路径
        file_path = get_default_file_path()
        if not file_path:
            print("❌ 无法获取默认文件路径，程序退出")
            return

        # 开始批量查询和处理
        print("\n" + "=" * 50)
        print(f"🚀 开始批量查询 {len(query_dates)} 个交易日的涨停数据")

        success_count = 0
        failed_dates = []

        for i, query_date in enumerate(query_dates, 1):
            print(f"\n进度: {i}/{len(query_dates)}")
            print("-" * 30)

            try:
                if process_single_date(query_date, file_path):
                    success_count += 1
                else:
                    failed_dates.append(query_date.strftime('%Y%m%d'))
            except Exception as e:
                print(f"❌ {query_date.strftime('%Y%m%d')} 处理异常: {str(e)}")
                failed_dates.append(query_date.strftime('%Y%m%d'))

        # 显示最终结果
        print("\n" + "=" * 50)
        print("🎉 批量处理完成！")
        print(f"✅ 成功处理: {success_count} 个交易日")

        if failed_dates:
            print(f"❌ 失败日期: {len(failed_dates)} 个")
            for date in failed_dates:
                print(f"  - {date}")

        print(f"📁 Excel文件路径: {file_path}")

        if success_count > 0:
            print(f"📊 已创建 {success_count} 个工作表，每个工作表对应一个交易日的涨停数据")

            # 如果是批量查询（多个日期），对工作表进行排序
            if len(query_dates) > 1:
                print("正在对工作表进行排序（最新日期在最左侧）...")
                sort_worksheets_by_date(file_path, query_dates)
                print("✅ 工作表排序完成！")

    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
