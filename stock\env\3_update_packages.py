#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新所有已安装的Python包
支持pip和conda包的更新
"""

import subprocess
import sys
import os
import argparse
import json
from datetime import datetime

def check_conda_available():
    """检查conda是否可用"""
    try:
        subprocess.run(['conda', '--version'],
                      capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def get_current_conda_env():
    """获取当前conda环境信息"""
    try:
        result = subprocess.run(['conda', 'info'],
                              capture_output=True, text=True, check=True)
        lines = result.stdout.split('\n')
        for line in lines:
            if 'active environment' in line:
                env_name = line.split(':')[1].strip()
                return env_name
        return 'base'
    except (subprocess.CalledProcessError, FileNotFoundError):
        return None

def check_stock_env():
    """检查是否在stock_env环境中，如果不是则提示"""
    current_env = get_current_conda_env()
    if current_env != 'stock_env':
        print(f"⚠️  当前环境: {current_env}")
        print("⚠️  建议在stock_env环境中运行此脚本")
        print("⚠️  请运行: conda activate stock_env")

        choice = input("是否继续在当前环境中操作? (y/N): ").strip().lower()
        if choice != 'y':
            print("已取消操作")
            sys.exit(0)
    else:
        print(f"✓ 当前环境: {current_env}")
    return current_env

def get_outdated_pip_packages():
    """获取过期的pip包列表"""
    try:
        print("检查过期的pip包...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'list', '--outdated', '--format=json'
        ], capture_output=True, text=True, check=True)
        
        outdated_packages = json.loads(result.stdout)
        return outdated_packages
    except subprocess.CalledProcessError as e:
        print(f"获取过期pip包列表失败: {e}")
        return []
    except json.JSONDecodeError:
        print("解析pip包信息失败")
        return []

def update_pip_packages(packages=None, update_all=False):
    """更新pip包"""
    if update_all:
        print("更新所有pip包...")
        outdated = get_outdated_pip_packages()
        if not outdated:
            print("✓ 所有pip包都是最新版本")
            return True
        
        print(f"发现 {len(outdated)} 个过期的pip包:")
        for pkg in outdated:
            print(f"  {pkg['name']}: {pkg['version']} -> {pkg['latest_version']}")
        
        confirm = input("\n是否继续更新? (y/N): ").strip().lower()
        if confirm != 'y':
            print("取消更新")
            return False
        
        success_count = 0
        for pkg in outdated:
            try:
                print(f"更新 {pkg['name']}...")
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '--upgrade', pkg['name']
                ], check=True)
                success_count += 1
                print(f"✓ {pkg['name']} 更新成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {pkg['name']} 更新失败: {e}")
        
        print(f"\n更新完成: {success_count}/{len(outdated)} 个包更新成功")
        return success_count > 0
    
    elif packages:
        print(f"更新指定的pip包: {', '.join(packages)}")
        success_count = 0
        for pkg in packages:
            try:
                print(f"更新 {pkg}...")
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '--upgrade', pkg
                ], check=True)
                success_count += 1
                print(f"✓ {pkg} 更新成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {pkg} 更新失败: {e}")
        
        print(f"\n更新完成: {success_count}/{len(packages)} 个包更新成功")
        return success_count > 0
    
    else:
        # 更新pip本身
        try:
            print("更新pip...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True)
            print("✓ pip更新成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ pip更新失败: {e}")
            return False

def update_conda_packages(update_all=False, env_name=None):
    """更新conda包"""
    if not check_conda_available():
        print("❌ conda不可用，跳过conda包更新")
        return False

    # 如果没有指定环境名，获取当前环境
    if env_name is None:
        env_name = get_current_conda_env()

    try:
        if update_all:
            print(f"更新环境 {env_name} 中的所有conda包...")

            # 检查是否有权限更新base环境中的conda
            print("检查conda更新权限...")
            try:
                # 尝试更新conda本身（在base环境中）
                print("尝试更新conda...")
                subprocess.run(['conda', 'update', '-n', 'base', '-c', 'defaults', 'conda', '--yes'],
                              check=True)
                print("✓ conda更新成功")
            except subprocess.CalledProcessError as e:
                print("⚠️  conda更新失败（可能是权限问题）")
                print("   这通常是因为Anaconda安装在系统目录，需要管理员权限")
                print("   建议：以管理员身份运行命令提示符，或跳过conda更新")

                choice = input("是否跳过conda更新，仅更新当前环境的包? (Y/n): ").strip().lower()
                if choice == 'n':
                    print("已取消conda包更新")
                    return False
                print("跳过conda更新，继续更新环境包...")

            # 更新指定环境的所有包
            print(f"更新环境 {env_name} 的所有包...")
            if env_name == 'base':
                # 如果是base环境，可能也有权限问题
                try:
                    subprocess.run(['conda', 'update', '--all', '--yes'], check=True)
                except subprocess.CalledProcessError:
                    print("⚠️  base环境更新失败（权限问题）")
                    print("   建议以管理员身份运行")
                    return False
            else:
                # 更新用户环境（通常有权限）
                subprocess.run(['conda', 'update', '-n', env_name, '--all', '--yes'], check=True)
            print(f"✓ 环境 {env_name} 的conda包更新成功")
        else:
            # 仅更新conda
            print("更新conda...")
            try:
                subprocess.run(['conda', 'update', '-n', 'base', '-c', 'defaults', 'conda', '--yes'],
                              check=True)
                print("✓ conda更新成功")
            except subprocess.CalledProcessError as e:
                print("⚠️  conda更新失败（可能是权限问题）")
                print("   这通常是因为Anaconda安装在系统目录，需要管理员权限")
                print("   建议：以管理员身份运行命令提示符")
                return False

        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ conda包更新失败: {e}")
        return False

def backup_current_packages():
    """备份当前包信息"""
    try:
        # 获取当前环境信息
        current_env = get_current_conda_env()

        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backup_base_dir = os.path.join(script_dir, "backup")

        # 创建backup目录（如果不存在）
        os.makedirs(backup_base_dir, exist_ok=True)

        # 创建带时间戳的备份子目录
        backup_dir = os.path.join(backup_base_dir, f"package_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(backup_dir, exist_ok=True)

        print(f"备份环境 {current_env} 的包信息到 {os.path.relpath(backup_dir)}/...")

        # 备份pip包
        result = subprocess.run([sys.executable, '-m', 'pip', 'freeze'],
                              capture_output=True, text=True, check=True)
        with open(os.path.join(backup_dir, "requirements_backup.txt"), 'w', encoding='utf-8') as f:
            f.write(f"# Backup created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Environment: {current_env}\n")
            f.write(f"# Environment location: {os.environ.get('CONDA_PREFIX', 'unknown')}\n\n")
            f.write(result.stdout)

        # 备份conda包（如果可用）
        if check_conda_available():
            try:
                # 导出当前环境配置
                if current_env == 'base':
                    result = subprocess.run(['conda', 'env', 'export'],
                                          capture_output=True, text=True, check=True)
                else:
                    result = subprocess.run(['conda', 'env', 'export', '-n', current_env],
                                          capture_output=True, text=True, check=True)
                with open(os.path.join(backup_dir, "environment_backup.yml"), 'w', encoding='utf-8') as f:
                    f.write(result.stdout)

                # 导出包列表
                if current_env == 'base':
                    result = subprocess.run(['conda', 'list', '--export'],
                                          capture_output=True, text=True, check=True)
                else:
                    result = subprocess.run(['conda', 'list', '-n', current_env, '--export'],
                                          capture_output=True, text=True, check=True)
                with open(os.path.join(backup_dir, "conda_packages_backup.txt"), 'w', encoding='utf-8') as f:
                    f.write(f"# Backup created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# Environment: {current_env}\n\n")
                    f.write(result.stdout)
            except subprocess.CalledProcessError as e:
                print(f"⚠️  conda备份部分失败: {e}")

        print(f"✓ 环境 {current_env} 的包信息已备份到 {os.path.relpath(backup_dir)}/")
        return backup_dir
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def interactive_update():
    """交互式更新包"""
    print("=" * 50)
    print("Python包更新工具 - stock_env环境")
    print("=" * 50)

    # 检查当前环境
    current_env = check_stock_env()
    print("=" * 50)

    print("选择更新方式:")
    print("1. 更新所有包 (pip + conda)")
    print("2. 仅更新pip包")
    print("3. 仅更新conda包")
    print("4. 更新指定的pip包")
    print("5. 仅更新pip和conda本身")
    print("6. 检查过期包 (不更新)")
    print("0. 退出")

    choice = input("\n请输入选择 (0-6): ").strip()
    
    if choice == "0":
        print("退出更新")
        return
    
    # 询问是否备份
    backup_choice = input("是否在更新前备份当前包信息? (Y/n): ").strip().lower()
    if backup_choice != 'n':
        backup_current_packages()
    
    if choice == "1":
        print(f"\n更新环境 {current_env} 的所有包...")
        update_conda_packages(update_all=True, env_name=current_env)
        update_pip_packages(update_all=True)

    elif choice == "2":
        print(f"\n更新环境 {current_env} 的pip包...")
        update_pip_packages(update_all=True)

    elif choice == "3":
        print(f"\n更新环境 {current_env} 的conda包...")
        update_conda_packages(update_all=True, env_name=current_env)
        
    elif choice == "4":
        packages_input = input("请输入要更新的包名 (用空格分隔): ").strip()
        if packages_input:
            packages = packages_input.split()
            update_pip_packages(packages=packages)
        else:
            print("❌ 未输入包名")
            
    elif choice == "5":
        print("\n更新pip和conda本身...")
        update_pip_packages()  # 更新pip本身
        update_conda_packages(env_name=current_env)  # 更新conda本身
        
    elif choice == "6":
        print("\n检查过期的pip包...")
        outdated = get_outdated_pip_packages()
        if outdated:
            print(f"发现 {len(outdated)} 个过期的pip包:")
            for pkg in outdated:
                print(f"  {pkg['name']}: {pkg['version']} -> {pkg['latest_version']}")
        else:
            print("✓ 所有pip包都是最新版本")
            
        if check_conda_available():
            print("\n检查conda包更新...")
            try:
                result = subprocess.run(['conda', 'search', '--outdated'], 
                                      capture_output=True, text=True, timeout=30)
                if result.stdout.strip():
                    print("conda包更新信息:")
                    print(result.stdout)
                else:
                    print("✓ conda包检查完成")
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                print("conda包检查超时或失败")
    else:
        print("❌ 无效的选择")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="更新Python包 - stock_env环境")
    parser.add_argument('--all', action='store_true', help='更新所有包')
    parser.add_argument('--pip-only', action='store_true', help='仅更新pip包')
    parser.add_argument('--conda-only', action='store_true', help='仅更新conda包')
    parser.add_argument('--packages', nargs='+', help='指定要更新的包名')
    parser.add_argument('--no-backup', action='store_true', help='不备份当前包信息')
    parser.add_argument('--check-only', action='store_true', help='仅检查过期包，不更新')
    parser.add_argument('--env', default=None, help='指定conda环境名（默认使用当前环境）')

    args = parser.parse_args()

    # 检查当前环境
    current_env = check_stock_env()
    target_env = args.env if args.env else current_env
    
    if args.check_only:
        print("检查过期包...")
        outdated = get_outdated_pip_packages()
        if outdated:
            print(f"发现 {len(outdated)} 个过期的pip包:")
            for pkg in outdated:
                print(f"  {pkg['name']}: {pkg['version']} -> {pkg['latest_version']}")
        else:
            print("✓ 所有pip包都是最新版本")
        return
    
    # 备份当前包信息
    if not args.no_backup:
        backup_current_packages()
    
    if args.all:
        print(f"更新环境 {target_env} 的所有包...")
        update_conda_packages(update_all=True, env_name=target_env)
        update_pip_packages(update_all=True)
    elif args.pip_only:
        print(f"更新环境 {target_env} 的pip包...")
        update_pip_packages(update_all=True)
    elif args.conda_only:
        print(f"更新环境 {target_env} 的conda包...")
        update_conda_packages(update_all=True, env_name=target_env)
    elif args.packages:
        print(f"更新环境 {target_env} 的指定包: {', '.join(args.packages)}")
        update_pip_packages(packages=args.packages)
    else:
        interactive_update()

if __name__ == "__main__":
    main()
